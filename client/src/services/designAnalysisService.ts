import { supabase } from '@/lib/supabase'
import { api } from '@/lib/api'
import type {
  DesignAnalysis,
  CreateDesignAnalysisData,
  UpdateDesignAnalysisData,
  DesignUpload,
  CreateDesignUploadData
} from '@/lib/supabase'

/**
 * Service for managing design analysis data in Supabase
 */
export class DesignAnalysisService {

  /**
   * Upload an image file to Supabase Storage with comprehensive error handling
   */
  async uploadImage(file: File, userId: string): Promise<string> {
    try {
      // Validate inputs
      if (!file) {
        throw new Error('No file provided for upload')
      }

      if (!userId) {
        throw new Error('User ID is required for upload')
      }

      // Check file size (10MB limit)
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (file.size > maxSize) {
        throw new Error(`File size (${(file.size / 1024 / 1024).toFixed(1)}MB) exceeds the 10MB limit`)
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      if (!allowedTypes.includes(file.type)) {
        throw new Error(`File type ${file.type} is not supported. Please use JPEG, PNG, GIF, WebP, or SVG`)
      }

      // Generate unique filename with user ID and timestamp
      // CRITICAL: Ensure userId is treated as string for RLS policy compatibility
      const timestamp = Date.now()
      const fileExtension = file.name.split('.').pop() || 'jpg'

      // ✅ FIXED: Preserve international characters while making filename storage-safe
      // Normalize Unicode characters to handle accented characters properly
      const normalizedName = file.name.normalize('NFD')

      // Keep Unicode letters, numbers, dots, hyphens, and underscores
      // Replace other characters with underscores
      const sanitizedName = normalizedName.replace(/[^\w\.\-]/gu, '_')
        .replace(/_+/g, '_') // Remove multiple consecutive underscores
        .replace(/^_|_$/g, '') // Trim underscores from start/end

      const userIdString = String(userId) // Ensure string type for RLS policy
      const fileName = `${userIdString}/${timestamp}_${sanitizedName || 'image'}`

      console.log('📝 Generated filename:', {
        originalName: file.name,
        sanitizedName,
        fileExtension,
        finalFileName: fileName
      })

      console.log('📤 Uploading image to Supabase Storage:', {
        fileName,
        fileSize: file.size,
        fileType: file.type
      })

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from('design-analysis-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        console.error('❌ Supabase Storage upload error:', {
          error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          fileName,
          fileSize: file.size,
          fileType: file.type,
          userId: userIdString
        })

        // Handle specific error types with more detailed information
        if (error.message.includes('Duplicate')) {
          throw new Error('A file with this name already exists. Please try again.')
        } else if (error.message.includes('Policy') || error.message.includes('policy') || error.message.includes('RLS')) {
          // Enhanced RLS policy error with debugging info
          console.error('🔍 RLS Policy Error Details:', {
            userIdInPath: userIdString,
            fileName,
            expectedAuthUid: 'Check if user.id matches auth.uid()',
            policyCheck: 'Verify RLS policy allows user to upload to their own folder'
          });
          throw new Error(`Storage permission error: You do not have permission to upload files. Check authentication and RLS policies. User ID: ${userIdString}`)
        } else if (error.message.includes('size')) {
          throw new Error('File size exceeds the allowed limit. Please use a smaller image.')
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          throw new Error('Network error during upload. Please check your connection and try again.')
        } else if (error.message.includes('bucket')) {
          throw new Error('Storage bucket is not available. Please try again later.')
        } else if (error.message.includes('authentication') || error.message.includes('unauthorized')) {
          throw new Error('Authentication error. Please log in again and try uploading.')
        } else {
          // Enhanced error message with more context
          throw new Error(`Upload failed: ${error.message}. Code: ${error.code || 'unknown'}. Please check your connection and authentication.`)
        }
      }

      if (!data?.path) {
        throw new Error('Upload completed but no file path was returned')
      }

      // For private buckets, store the file path instead of public URL
      // The getImageUrl method will handle creating signed URLs when needed
      console.log('✅ Image uploaded successfully:', {
        path: data.path,
        fileName
      })

      // Return the file path (not public URL) for private bucket storage
      return data.path
    } catch (error) {
      console.error('💥 Error in uploadImage:', error)

      // Re-throw with more context if it's a generic error
      if (error instanceof Error) {
        throw error
      } else {
        throw new Error('An unexpected error occurred during image upload')
      }
    }
  }

  /**
   * Check for duplicate analysis entries to prevent race condition duplicates
   */
  private async checkForDuplicateAnalysis(
    userId: string,
    filename: string,
    fileSize: number,
    timeWindowSeconds: number = 30
  ): Promise<DesignAnalysis | null> {
    try {
      const timeThreshold = new Date(Date.now() - timeWindowSeconds * 1000).toISOString()

      const { data, error } = await supabase
        .from('design_analyses')
        .select('*')
        .eq('user_id', userId)
        .eq('original_filename', filename)
        .eq('file_size', fileSize)
        .gte('created_at', timeThreshold)
        .order('created_at', { ascending: false })
        .limit(1)

      if (error) {
        console.warn('Warning: Could not check for duplicates:', error)
        return null
      }

      return data.length > 0 ? data[0] : null
    } catch (error) {
      console.warn('Warning: Duplicate check failed:', error)
      return null
    }
  }

  /**
   * Save a new design analysis to the database with optional image upload and comprehensive error handling
   */
  async saveAnalysis(analysisData: CreateDesignAnalysisData, imageFile?: File): Promise<DesignAnalysis> {
    let uploadedImageUrl: string | null = null

    try {
      // ✅ FIXED: Check for duplicate analysis before saving
      if (analysisData.user_id && analysisData.original_filename && analysisData.file_size) {
        console.log('🔍 Checking for duplicate analysis...', {
          userId: analysisData.user_id,
          filename: analysisData.original_filename,
          fileSize: analysisData.file_size
        })

        const existingAnalysis = await this.checkForDuplicateAnalysis(
          analysisData.user_id,
          analysisData.original_filename,
          analysisData.file_size,
          30 // 30-second window
        )

        if (existingAnalysis) {
          console.log('⚠️ Duplicate analysis detected - returning existing analysis:', {
            existingId: existingAnalysis.id,
            existingCreatedAt: existingAnalysis.created_at,
            hasFileUrl: !!existingAnalysis.file_url
          })

          // Return the existing analysis instead of creating a duplicate
          return existingAnalysis
        }

        console.log('✅ No duplicate found - proceeding with save')
      }

      let finalAnalysisData = { ...analysisData }

      // Upload image if provided
      if (imageFile && analysisData.user_id) {
        console.log('💾 Starting image upload for analysis save...', {
          fileName: imageFile.name,
          fileSize: imageFile.size,
          fileType: imageFile.type,
          userId: analysisData.user_id
        })

        try {
          uploadedImageUrl = await this.uploadImage(imageFile, analysisData.user_id)
          finalAnalysisData.file_url = uploadedImageUrl
          console.log('✅ Image uploaded successfully, proceeding with analysis save', {
            uploadedPath: uploadedImageUrl,
            finalDataFileUrl: finalAnalysisData.file_url
          })
        } catch (uploadError) {
          console.error('❌ Image upload failed:', uploadError)

          // Check if it's a critical error or if we can continue without image
          const errorMessage = uploadError instanceof Error ? uploadError.message : 'Unknown error'

          // Log detailed error information for debugging
          console.error('🔍 Upload error details:', {
            errorMessage,
            errorType: uploadError instanceof Error ? uploadError.constructor.name : typeof uploadError,
            stack: uploadError instanceof Error ? uploadError.stack : undefined,
            fileName: imageFile.name,
            fileSize: imageFile.size,
            fileType: imageFile.type,
            userId: analysisData.user_id
          });

          // FIXED: Be more strict about upload errors - most should be critical
          if (errorMessage.includes('permission') || errorMessage.includes('authentication')) {
            // Critical auth errors - don't save analysis
            throw new Error(`Authentication error: ${errorMessage}`)
          } else if (errorMessage.includes('size') || errorMessage.includes('type')) {
            // File validation errors - don't save analysis
            throw new Error(`File validation error: ${errorMessage}`)
          } else if (errorMessage.includes('Policy') || errorMessage.includes('RLS') || errorMessage.includes('policy')) {
            // RLS policy errors - critical
            throw new Error(`Storage permission error: ${errorMessage}`)
          } else if (errorMessage.includes('Duplicate') || errorMessage.includes('duplicate')) {
            // Duplicate file errors - retry with different name or fail
            throw new Error(`File already exists: ${errorMessage}`)
          } else if (errorMessage.includes('network') || errorMessage.includes('fetch') || errorMessage.includes('timeout')) {
            // Network errors - these should be retried, not ignored
            throw new Error(`Network error during upload: ${errorMessage}`)
          } else {
            // CHANGED: Unknown errors should also be critical - don't save without image
            console.error('💥 Unknown upload error - this should be investigated:', errorMessage)
            throw new Error(`Upload failed: ${errorMessage}`)
          }
        }
      }

      console.log('💾 Saving analysis to database...', {
        hasFileUrl: !!finalAnalysisData.file_url,
        fileUrl: finalAnalysisData.file_url,
        originalFilename: finalAnalysisData.original_filename,
        userId: finalAnalysisData.user_id
      })

      const { data, error } = await supabase
        .from('design_analyses')
        .insert(finalAnalysisData)
        .select()
        .single()

      if (error) {
        console.error('❌ Database save error:', error)

        // If we uploaded an image but database save failed, we should clean up the image
        if (uploadedImageUrl && imageFile) {
          console.log('🧹 Cleaning up uploaded image due to database save failure...')
          try {
            // uploadedImageUrl is already a file path (userId/filename), not a full URL
            await supabase.storage
              .from('design-analysis-images')
              .remove([uploadedImageUrl])
            console.log('✅ Cleanup completed')
          } catch (cleanupError) {
            console.error('❌ Failed to cleanup uploaded image:', cleanupError)
          }
        }

        // Handle specific database errors
        if (error.code === 'PGRST106') {
          throw new Error('Database schema configuration error. Please contact support.')
        } else if (error.message.includes('duplicate') || error.code === '23505') {
          throw new Error('An analysis with similar data already exists')
        } else if (error.message.includes('permission') || error.code === '42501') {
          throw new Error('You do not have permission to save analyses. Please check your authentication.')
        } else if (error.message.includes('network') || error.message.includes('connection')) {
          throw new Error('Network error while saving. Please check your connection and try again.')
        } else {
          throw new Error(`Failed to save analysis: ${error.message}`)
        }
      }

      if (!data) {
        throw new Error('Analysis was saved but no data was returned')
      }

      console.log('✅ Analysis saved successfully:', data.id)
      return data
    } catch (error) {
      console.error('💥 Error in saveAnalysis:', error)

      // Re-throw with more context if it's a generic error
      if (error instanceof Error) {
        throw error
      } else {
        throw new Error('An unexpected error occurred while saving the analysis')
      }
    }
  }

  /**
   * Save a new design analysis to the database (legacy method without image upload)
   */
  async saveAnalysisLegacy(analysisData: CreateDesignAnalysisData): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .from('design_analyses')
      .insert(analysisData)
      .select()
      .single()

    if (error) {
      console.error('Error saving design analysis:', error)

      // Handle specific database errors
      if (error.code === 'PGRST106') {
        throw new Error('Database schema configuration error. Please contact support.')
      } else if (error.message?.includes('permission denied') || error.message?.includes('RLS')) {
        throw new Error('Access denied. Please check your authentication.')
      } else {
        throw new Error(`Failed to save analysis: ${error.message}`)
      }
    }

    return data
  }

  /**
   * Retrieve image from Supabase Storage using Backend Image Proxy
   * Uses the backend /api/image/{file_path} endpoint for reliable image serving
   * This method handles authentication and RLS policies on the backend
   */
  async getImageUrl(filePath: string): Promise<string | null> {
    try {
      console.log('🔍 getImageUrl called with:', { filePath, type: typeof filePath });

      if (!filePath) {
        console.warn('⚠️ No file path provided to getImageUrl')
        return null
      }

      // First, try the new backend image proxy method
      console.log('🎯 Using backend image proxy for reliable image serving');
      const backendUrl = await this.getImageUrlFromBackend(filePath);
      if (backendUrl) {
        console.log('✅ Backend image proxy successful');
        return backendUrl;
      }

      // Fallback to direct Supabase access if backend fails
      console.log('🔄 Backend proxy failed, trying direct Supabase access...');
      return await this.getImageUrlDirectSupabase(filePath);

    } catch (error) {
      console.error('💥 Error in getImageUrl:', error);
      return null;
    }
  }

  /**
   * Get image URL using the backend image proxy (RECOMMENDED METHOD)
   * This method uses the /api/image/{file_path} endpoint for reliable image serving
   */
  async getImageUrlFromBackend(filePath: string): Promise<string | null> {
    try {
      console.log('🔗 Getting image URL from backend proxy:', filePath);

      // Ensure we have a valid authenticated session for the backend request
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      if (sessionError || !session?.access_token) {
        console.error('❌ No valid session for backend request:', sessionError)
        return null
      }

      // If it's already a full URL (legacy data), try to extract the file path
      if (filePath.startsWith('http')) {
        console.log('🔗 File path is a full URL, extracting path:', filePath)
        try {
          const url = new URL(filePath)
          // Extract the file path from the URL (last two segments: userId/filename)
          const pathSegments = url.pathname.split('/').filter(segment => segment)
          console.log('📝 URL path segments:', pathSegments);

          if (pathSegments.length >= 2) {
            filePath = pathSegments.slice(-2).join('/')
            console.log('📝 Extracted file path:', filePath)
          } else {
            console.warn('⚠️ Could not extract valid file path from URL - insufficient segments')
            return null
          }
        } catch (urlError) {
          console.warn('⚠️ Invalid URL format:', urlError)
          return null
        }
      }

      // Use the backend image proxy endpoint
      const imageEndpoint = `/api/image/${encodeURIComponent(filePath).replace(/%2F/g, '/')}`;
      console.log('🔗 Backend image endpoint:', imageEndpoint);

      // Make authenticated request to backend
      const response = await api.get(imageEndpoint, {
        responseType: 'blob',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (response.status === 200 && response.data) {
        // Create blob URL for the image
        const blob = response.data;
        const imageUrl = URL.createObjectURL(blob);

        console.log('✅ Backend image proxy successful:', {
          blobSize: blob.size,
          blobType: blob.type,
          method: 'backend_proxy'
        });

        return imageUrl;
      } else {
        console.warn('⚠️ Backend image proxy failed:', response.status);
        return null;
      }

    } catch (error) {
      console.error('💥 Backend image proxy error:', error);
      return null;
    }
  }

  /**
   * Get image URL using direct Supabase access (FALLBACK METHOD)
   * This method uses Supabase authenticated endpoints directly
   */
  async getImageUrlDirectSupabase(filePath: string): Promise<string | null> {
    try {
      console.log('🔄 Using direct Supabase access for:', filePath);

      // Ensure we have a valid authenticated session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      if (sessionError || !session?.access_token) {
        console.error('❌ No valid session for direct Supabase access:', sessionError)
        return null
      }

      // Check if the file path is already a full URL (http/https)
      if (filePath.startsWith('http')) {
        console.log('🔗 File path is already a full URL:', filePath.substring(0, 50) + '...')

        // Test if the URL is accessible
        try {
          const testResponse = await fetch(filePath, { method: 'HEAD' })
          if (testResponse.ok) {
            console.log('✅ URL is accessible, returning as-is')
            return filePath
          } else {
            console.warn('⚠️ URL not accessible, status:', testResponse.status)
            // Continue with Supabase download method below
          }
        } catch (urlError) {
          console.warn('⚠️ URL test failed:', urlError.message)
          // Continue with Supabase download method below
        }
      }

      console.log('📥 Loading image from private bucket using authenticated endpoint:', filePath)

      // Get Supabase project URL from the client
      const supabaseUrl = supabase.supabaseUrl

      // Construct the authenticated endpoint URL with proper encoding
      // Format: https://[project].supabase.co/storage/v1/object/authenticated/[bucket]/[path]
      // ✅ FIXED: Properly encode file path to handle international characters
      const encodedFilePath = encodeURIComponent(filePath).replace(/%2F/g, '/') // Keep forward slashes unencoded
      const authenticatedUrl = `${supabaseUrl}/storage/v1/object/authenticated/design-analysis-images/${encodedFilePath}`

      console.log('🔗 Authenticated URL:', authenticatedUrl.substring(0, 80) + '...')

      // Retry logic for network issues
      let lastError: any = null
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          console.log(`🔄 Fetch attempt ${attempt}/3`)

          // Use fetch with proper authentication headers for private bucket
          const response = await fetch(authenticatedUrl, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
              'apikey': supabase.supabaseKey,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            const errorText = await response.text()
            lastError = new Error(`HTTP ${response.status}: ${errorText}`)

            console.warn(`⚠️ Fetch attempt ${attempt} failed:`, {
              status: response.status,
              statusText: response.statusText,
              error: errorText,
              filePath: filePath,
              url: authenticatedUrl.substring(0, 80) + '...'
            });

            // Don't retry for certain errors
            if (response.status === 404 || response.status === 403 || response.status === 401) {
              break // Don't retry for these errors
            }

            // Wait before retry (exponential backoff)
            if (attempt < 3) {
              await new Promise(resolve => setTimeout(resolve, attempt * 1000))
              continue
            }
          } else {
            // Get the blob from the response
            const fileBlob = await response.blob()

            // Validate the blob
            if (fileBlob.size === 0) {
              console.warn('⚠️ Downloaded file is empty');
              return null;
            }

            if (!fileBlob.type.startsWith('image/')) {
              console.warn('⚠️ Downloaded file is not an image:', fileBlob.type);
              return null;
            }

            // Create object URL for display
            const objectUrl = URL.createObjectURL(fileBlob)
            console.log('✅ Object URL created successfully:', {
              blobSize: fileBlob.size,
              blobType: fileBlob.type,
              objectUrl: objectUrl.substring(0, 50) + '...',
              attempt: attempt,
              method: 'authenticated_endpoint'
            });
            return objectUrl
          }
        } catch (networkError) {
          lastError = networkError
          console.warn(`🌐 Network error on attempt ${attempt}:`, networkError)

          if (attempt < 3) {
            await new Promise(resolve => setTimeout(resolve, attempt * 1000))
            continue
          }
        }
      }

      // All attempts failed, provide specific error messages
      if (lastError) {
        if (lastError.message.includes('404') || lastError.message.includes('not found')) {
          console.warn('📁 File not found in storage bucket');
        } else if (lastError.message.includes('403') || lastError.message.includes('401') || lastError.message.includes('permission')) {
          console.warn('🔒 Permission denied - check RLS policies and authentication');
        } else if (lastError.message.includes('network') || lastError.message.includes('fetch')) {
          console.warn('🌐 Network error during download');
        }
      }

      return null

    } catch (error) {
      console.error('💥 Error in getImageUrl:', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        filePath,
        stack: error instanceof Error ? error.stack : undefined
      });
      return null
    }
  }

  /**
   * Fallback method using Supabase SDK download (for comparison/debugging)
   */
  async getImageUrlFallback(filePath: string): Promise<string | null> {
    try {
      console.log('🔄 Using fallback method (SDK download) for:', filePath);

      // For private buckets, use authenticated download to get the file
      const { data: fileBlob, error: downloadError } = await supabase.storage
        .from('design-analysis-images')
        .download(filePath)

      if (downloadError) {
        console.warn('⚠️ Fallback download failed:', downloadError.message);
        return null
      }

      if (!fileBlob || fileBlob.size === 0) {
        console.warn('⚠️ Fallback: No blob or empty file');
        return null;
      }

      if (!fileBlob.type.startsWith('image/')) {
        console.warn('⚠️ Fallback: Downloaded file is not an image:', fileBlob.type);
        return null;
      }

      // Create object URL for display
      const objectUrl = URL.createObjectURL(fileBlob)
      console.log('✅ Fallback object URL created:', {
        blobSize: fileBlob.size,
        blobType: fileBlob.type,
        method: 'sdk_download'
      });
      return objectUrl

    } catch (error) {
      console.error('💥 Error in fallback method:', error);
      return null
    }
  }

  /**
   * Smart image URL retrieval with automatic fallback
   * Tries the primary method first, falls back to SDK download if needed
   */
  async getImageUrlWithFallback(filePath: string): Promise<string | null> {
    console.log('🎯 Smart image retrieval with fallback for:', filePath);

    // Try primary method first (authenticated endpoint)
    const primaryUrl = await this.getImageUrl(filePath);
    if (primaryUrl) {
      console.log('✅ Primary method succeeded');
      return primaryUrl;
    }

    // If primary fails, try fallback method
    console.log('🔄 Primary method failed, trying fallback...');
    const fallbackUrl = await this.getImageUrlFallback(filePath);
    if (fallbackUrl) {
      console.log('✅ Fallback method succeeded');
      return fallbackUrl;
    }

    console.log('❌ Both methods failed');
    return null;
  }

  /**
   * Check if an image exists in Supabase Storage
   */
  async checkImageExists(filePath: string): Promise<boolean> {
    try {
      if (!filePath) return false

      // Extract the file path from URL if needed
      let actualPath = filePath
      if (filePath.startsWith('http')) {
        const url = new URL(filePath)
        actualPath = url.pathname.split('/').slice(-2).join('/') // Get user_id/filename
      }

      const { data, error } = await supabase.storage
        .from('design-analysis-images')
        .list(actualPath.split('/')[0], {
          search: actualPath.split('/')[1]
        })

      if (error) {
        console.error('Error checking if image exists:', error)
        return false
      }

      return data && data.length > 0
    } catch (error) {
      console.error('Error in checkImageExists:', error)
      return false
    }
  }

  /**
   * Test Supabase Storage connection and permissions
   */
  async testStorageConnection(userId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Test bucket access by listing files
      const { data, error } = await supabase.storage
        .from('design-analysis-images')
        .list(userId, { limit: 1 })

      if (error) {
        if (error.message.includes('permission') || error.message.includes('policy')) {
          return {
            success: false,
            message: 'No tienes permisos para acceder al almacenamiento. Verifica tu autenticación.'
          }
        } else if (error.message.includes('bucket')) {
          return {
            success: false,
            message: 'El bucket de almacenamiento no está disponible.'
          }
        } else {
          return {
            success: false,
            message: `Error de conexión: ${error.message}`
          }
        }
      }

      return {
        success: true,
        message: 'Conexión al almacenamiento exitosa'
      }
    } catch (error) {
      return {
        success: false,
        message: `Error inesperado: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Test upload functionality with a small test file
   */
  async testUpload(): Promise<{ success: boolean; details: any; message: string }> {
    try {
      console.log('🧪 Starting upload test...')

      // 1. Check authentication
      const { data: { user, session }, error: authError } = await supabase.auth.getUser()
      if (authError || !user || !session) {
        return {
          success: false,
          details: { step: 'authentication', error: authError },
          message: 'Authentication failed'
        }
      }

      console.log('✅ Authentication check passed:', {
        userId: user.id,
        email: user.email,
        hasAccessToken: !!session.access_token
      })

      // 2. Create a test image file (1x1 pixel PNG)
      const canvas = document.createElement('canvas')
      canvas.width = 1
      canvas.height = 1
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.fillStyle = '#FF0000'
        ctx.fillRect(0, 0, 1, 1)
      }

      const testImageBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/png')
      })

      const testFile = new File([testImageBlob], 'upload-test.png', {
        type: 'image/png'
      })

      console.log('✅ Test file created:', {
        name: testFile.name,
        size: testFile.size,
        type: testFile.type
      })

      // 3. Test upload
      console.log('📤 Testing upload...')
      const uploadResult = await this.uploadImage(testFile, user.id)

      if (uploadResult) {
        console.log('✅ Upload successful:', uploadResult)

        // 4. Verify the file exists
        const exists = await this.checkImageExists(uploadResult)
        console.log('🔍 File existence check:', exists)

        // 5. Test retrieval
        const imageUrl = await this.getImageUrl(uploadResult)
        const retrievalSuccess = !!imageUrl

        if (imageUrl && imageUrl.startsWith('blob:')) {
          URL.revokeObjectURL(imageUrl)
        }

        // 6. Clean up test file
        await supabase.storage
          .from('design-analysis-images')
          .remove([uploadResult])
        console.log('🧹 Test file cleaned up')

        return {
          success: true,
          details: {
            uploadPath: uploadResult,
            fileExists: exists,
            retrievalWorks: retrievalSuccess,
            userId: user.id,
            testFileSize: testFile.size
          },
          message: 'Upload test successful - all components working'
        }
      } else {
        return {
          success: false,
          details: { step: 'upload', error: 'Upload returned null' },
          message: 'Upload failed - returned null'
        }
      }

    } catch (error) {
      console.error('💥 Upload test failed:', error)
      return {
        success: false,
        details: {
          step: 'upload_test',
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        },
        message: `Upload test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Comprehensive test of the complete image upload and retrieval flow
   */
  async testImageFlow(): Promise<{ success: boolean; details: any; message: string }> {
    try {
      console.log('🧪 Starting comprehensive image flow test...')

      // 1. Check authentication
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      if (authError || !user) {
        return {
          success: false,
          details: { step: 'authentication', error: authError },
          message: 'Authentication failed'
        }
      }

      // 2. Test storage connection
      const storageTest = await this.testStorageConnection(user.id)
      if (!storageTest.success) {
        return {
          success: false,
          details: { step: 'storage_connection', error: storageTest.message },
          message: storageTest.message
        }
      }

      // 3. List user files to test if any exist
      const { data: files, error: listError } = await supabase.storage
        .from('design-analysis-images')
        .list(user.id, { limit: 5 })

      if (listError) {
        return {
          success: false,
          details: { step: 'file_listing', error: listError },
          message: `File listing failed: ${listError.message}`
        }
      }

      // 4. If files exist, test both image retrieval methods
      if (files && files.length > 0) {
        const testFile = files[0]
        const filePath = `${user.id}/${testFile.name}`

        console.log('🧪 Testing image retrieval for:', filePath)

        // Test primary method (authenticated endpoint)
        const imageUrl = await this.getImageUrl(filePath)
        let primaryTestResult = false

        if (imageUrl) {
          primaryTestResult = await new Promise<boolean>((resolve) => {
            const img = new Image()
            img.onload = () => resolve(true)
            img.onerror = () => resolve(false)
            img.src = imageUrl
            setTimeout(() => resolve(false), 5000) // 5 second timeout
          })

          // Clean up
          if (imageUrl.startsWith('blob:')) {
            URL.revokeObjectURL(imageUrl)
          }
        }

        // Test fallback method (SDK download) for comparison
        const fallbackUrl = await this.getImageUrlFallback(filePath)
        let fallbackTestResult = false

        if (fallbackUrl) {
          fallbackTestResult = await new Promise<boolean>((resolve) => {
            const img = new Image()
            img.onload = () => resolve(true)
            img.onerror = () => resolve(false)
            img.src = fallbackUrl
            setTimeout(() => resolve(false), 5000) // 5 second timeout
          })

          // Clean up
          if (fallbackUrl.startsWith('blob:')) {
            URL.revokeObjectURL(fallbackUrl)
          }
        }

        const success = primaryTestResult || fallbackTestResult

        return {
          success,
          details: {
            step: 'image_retrieval',
            fileCount: files.length,
            testFile: testFile.name,
            primaryMethod: {
              urlGenerated: !!imageUrl,
              imageLoads: primaryTestResult,
              method: 'authenticated_endpoint'
            },
            fallbackMethod: {
              urlGenerated: !!fallbackUrl,
              imageLoads: fallbackTestResult,
              method: 'sdk_download'
            },
            recommendedMethod: primaryTestResult ? 'primary' : fallbackTestResult ? 'fallback' : 'none'
          },
          message: success
            ? `Image flow test successful using ${primaryTestResult ? 'authenticated endpoint' : 'SDK download'}`
            : 'Both image retrieval methods failed'
        }
      } else {
        return {
          success: true,
          details: {
            step: 'complete',
            fileCount: 0,
            message: 'No files to test, but storage connection is working'
          },
          message: 'Storage connection working, no files to test'
        }
      }

    } catch (error) {
      return {
        success: false,
        details: { step: 'unknown', error: error instanceof Error ? error.message : 'Unknown error' },
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Get all design analyses for the current user
   */
  async getUserAnalyses(userId: string, options?: {
    limit?: number
    offset?: number
    toolType?: string
    isFavorite?: boolean
    orderBy?: 'created_at' | 'updated_at' | 'overall_score'
    orderDirection?: 'asc' | 'desc'
  }): Promise<DesignAnalysis[]> {
    try {
      console.log('🔍 getUserAnalyses called with:', { userId, options });

      // Validate user ID
      if (!userId || userId === 'anonymous') {
        console.warn('⚠️ Invalid user ID provided to getUserAnalyses');
        return [];
      }

      // Check authentication first
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        console.error('❌ Authentication error in getUserAnalyses:', authError);
        throw new Error('User not authenticated');
      }

      if (user.id !== userId) {
        console.error('❌ User ID mismatch in getUserAnalyses:', { requestedUserId: userId, actualUserId: user.id });
        throw new Error('User ID mismatch');
      }

      // Build query with explicit column selection
      // NOTE: Supabase client is now configured to use 'api' schema which provides access to the data
      let query = supabase
        .from('design_analyses')
        .select(`
          id,
          created_at,
          updated_at,
          user_id,
          original_filename,
          file_size,
          file_type,
          file_url,
          tool_type,
          analysis_version,
          overall_score,
          complexity_scores,
          analysis_areas,
          recommendations,
          ai_analysis_summary,
          gemini_analysis,
          agent_message,
          visuai_insights,
          analysis_duration_ms,
          status,
          error_message,
          is_favorite,
          tags,
          notes,
          last_viewed_at,
          view_count
        `)
        .eq('user_id', userId);

      // Apply filters
      if (options?.toolType) {
        query = query.eq('tool_type', options.toolType);
      }

      if (options?.isFavorite !== undefined) {
        query = query.eq('is_favorite', options.isFavorite);
      }

      // Apply ordering
      const orderBy = options?.orderBy || 'created_at';
      const orderDirection = options?.orderDirection || 'desc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      console.log('🔍 Executing Supabase query for getUserAnalyses...');
      const { data, error } = await query;

      if (error) {
        console.error('❌ Supabase error in getUserAnalyses:', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          userId,
          options
        });

        // Provide specific error messages for common issues
        if (error.code === 'PGRST106') {
          throw new Error('Database schema configuration error. Please contact support.');
        } else if (error.message?.includes('permission denied') || error.message?.includes('RLS')) {
          throw new Error('Access denied. Please check your authentication.');
        } else {
          throw new Error(`Failed to fetch analyses: ${error.message}`);
        }
      }

      console.log('✅ getUserAnalyses successful:', {
        userId,
        analysesCount: data?.length || 0,
        options
      });

      return data || [];
    } catch (error) {
      console.error('💥 Exception in getUserAnalyses:', error);
      throw error;
    }
  }

  /**
   * Get a specific design analysis by ID
   */
  async getAnalysisById(id: string): Promise<DesignAnalysis | null> {
    const { data, error } = await supabase
      .from('design_analyses')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Error fetching analysis:', error)
      throw new Error(`Failed to fetch analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Update a design analysis
   */
  async updateAnalysis(updateData: UpdateDesignAnalysisData): Promise<DesignAnalysis> {
    const { id, ...updates } = updateData

    const { data, error } = await supabase
      .from('design_analyses')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating analysis:', error)
      throw new Error(`Failed to update analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a design analysis
   */
  async deleteAnalysis(id: string): Promise<void> {
    const { error } = await supabase
      .from('design_analyses')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting analysis:', error)
      throw new Error(`Failed to delete analysis: ${error.message}`)
    }
  }

  /**
   * Get recent design analyses (last 10)
   */
  async getRecentAnalyses(): Promise<DesignAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    return this.getUserAnalyses(user.id, {
      limit: 10,
      orderBy: 'created_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Get favorite design analyses
   */
  async getFavoriteAnalyses(): Promise<DesignAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    return this.getUserAnalyses(user.id, {
      isFavorite: true,
      orderBy: 'created_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Toggle favorite status of an analysis
   */
  async toggleFavorite(id: string): Promise<DesignAnalysis> {
    // First get the current analysis to toggle its favorite status
    const currentAnalysis = await this.getAnalysisById(id)
    if (!currentAnalysis) {
      throw new Error('Analysis not found')
    }

    const newFavoriteStatus = !currentAnalysis.is_favorite

    const { data, error } = await supabase
      .from('design_analyses')
      .update({ is_favorite: newFavoriteStatus })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error toggling favorite:', error)
      throw new Error(`Failed to toggle favorite: ${error.message}`)
    }

    return data
  }

  /**
   * Rename an analysis
   */
  async renameAnalysis(id: string, newName: string): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .from('design_analyses')
      .update({ custom_name: newName.trim() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error renaming analysis:', error)
      throw new Error(`Failed to rename analysis: ${error.message}`)
    }

    return data
  }

  /**
   * Record view for an analysis
   */
  async recordView(id: string): Promise<void> {
    const currentAnalysis = await this.getAnalysisById(id)
    if (!currentAnalysis) {
      return
    }

    // Update view count and last viewed timestamp
    const updateData = {
      view_count: (currentAnalysis.view_count || 0) + 1,
      last_viewed_at: new Date().toISOString()
    }

    const { error } = await supabase
      .from('design_analyses')
      .update(updateData)
      .eq('id', id)

    if (error) {
      console.error('Error recording view:', error)
    } else {
      console.log('✅ View recorded successfully for analysis:', id)
    }
  }

  /**
   * Add tags to an analysis
   */
  async updateTags(id: string, tags: string[]): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .from('design_analyses')
      .update({ tags })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating tags:', error)
      throw new Error(`Failed to update tags: ${error.message}`)
    }

    return data
  }

  /**
   * Add notes to an analysis
   */
  async updateNotes(id: string, notes: string): Promise<DesignAnalysis> {
    const { data, error } = await supabase
      .from('design_analyses')
      .update({ notes })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating notes:', error)
      throw new Error(`Failed to update notes: ${error.message}`)
    }

    return data
  }

  /**
   * Save uploaded file information
   */
  async saveUpload(uploadData: CreateDesignUploadData): Promise<DesignUpload> {
    const { data, error } = await supabase
      .from('design_uploads')
      .insert(uploadData)
      .select()
      .single()

    if (error) {
      console.error('Error saving upload:', error)
      throw new Error(`Failed to save upload: ${error.message}`)
    }

    return data
  }

  /**
   * Get user's upload history
   */
  async getUserUploads(userId: string, limit = 50): Promise<DesignUpload[]> {
    const { data, error } = await supabase
      .from('design_uploads')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching uploads:', error)
      throw new Error(`Failed to fetch uploads: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get analysis statistics for a user
   */
  async getUserStats(userId: string): Promise<{
    totalAnalyses: number
    favoriteAnalyses: number
    averageScore: number
    toolTypeBreakdown: Record<string, number>
    recentActivity: number // analyses in last 7 days
  }> {
    try {
      console.log('📊 getUserStats called with userId:', userId);

      // Validate user ID
      if (!userId || userId === 'anonymous') {
        console.warn('⚠️ Invalid user ID provided to getUserStats');
        return {
          totalAnalyses: 0,
          favoriteAnalyses: 0,
          averageScore: 0,
          toolTypeBreakdown: {},
          recentActivity: 0
        };
      }

      // Check authentication first
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        console.error('❌ Authentication error in getUserStats:', authError);
        throw new Error('User not authenticated');
      }

      if (user.id !== userId) {
        console.error('❌ User ID mismatch in getUserStats:', { requestedUserId: userId, actualUserId: user.id });
        throw new Error('User ID mismatch');
      }

      // Get total count and favorites with explicit column selection
      // NOTE: Using 'api' schema configuration for consistent access
      console.log('🔍 Executing Supabase query for getUserStats...');
      const { data: analyses, error } = await supabase
        .from('design_analyses')
        .select('overall_score, tool_type, is_favorite, created_at')
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Supabase error in getUserStats:', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          userId
        });

        // Provide specific error messages for common issues
        if (error.code === 'PGRST106') {
          throw new Error('Database schema configuration error. Please contact support.');
        } else if (error.message?.includes('permission denied') || error.message?.includes('RLS')) {
          throw new Error('Access denied. Please check your authentication.');
        } else {
          throw new Error(`Failed to fetch user stats: ${error.message}`);
        }
      }

      if (!analyses || analyses.length === 0) {
        console.log('📊 No analyses found for user stats');
        return {
          totalAnalyses: 0,
          favoriteAnalyses: 0,
          averageScore: 0,
          toolTypeBreakdown: {},
          recentActivity: 0
        };
      }

      const totalAnalyses = analyses.length;
      const favoriteAnalyses = analyses.filter(a => a.is_favorite).length;
      const averageScore = analyses.reduce((sum, a) => sum + a.overall_score, 0) / totalAnalyses;

      // Tool type breakdown
      const toolTypeBreakdown: Record<string, number> = {};
      analyses.forEach(a => {
        toolTypeBreakdown[a.tool_type] = (toolTypeBreakdown[a.tool_type] || 0) + 1;
      });

      // Recent activity (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const recentActivity = analyses.filter(a =>
        new Date(a.created_at) > sevenDaysAgo
      ).length;

      console.log('✅ getUserStats successful:', {
        totalAnalyses,
        favoriteAnalyses,
        averageScore: Math.round(averageScore * 100) / 100,
        toolTypeBreakdown,
        recentActivity
      });

      return {
        totalAnalyses,
        favoriteAnalyses,
        averageScore: Math.round(averageScore * 100) / 100,
        toolTypeBreakdown,
        recentActivity
      };
    } catch (error) {
      console.error('💥 Exception in getUserStats:', error);
      throw error;
    }
  }


}

// Export singleton instance
export const designAnalysisService = new DesignAnalysisService()
